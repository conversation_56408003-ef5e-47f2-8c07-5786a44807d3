"""
Activations store for collecting and managing model activations.

This module provides utilities for extracting, storing, and batching
activations from transformer models for SAE training.
"""

import logging
from typing import Optional, Dict, Any, Iterator, Callable, List, Tuple
import torch
from torch.utils.data import DataLoader
from transformers import PreTrainedModel, PreTrainedTokenizer
from datasets import Dataset
import numpy as np
from collections import defaultdict
import threading
from queue import Queue, Empty

from .config import SAEConfig
from .dataset_manager import DatasetManager

logger = logging.getLogger(__name__)


class ActivationHook:
    """Hook for capturing activations from model layers."""

    def __init__(self, layer_name: str, hook_point: str = "output"):
        """
        Initialize activation hook.

        Args:
            layer_name: Name of the layer to hook
            hook_point: Point in layer to hook (input, output, or custom)
        """
        self.layer_name = layer_name
        self.hook_point = hook_point
        self.activations = []
        self.hook_handle = None

    def hook_fn(self, module, input, output):
        """Hook function to capture activations."""
        if self.hook_point == "input":
            if isinstance(input, tuple):
                activation = input[0]
            else:
                activation = input
        elif self.hook_point == "output":
            if isinstance(output, tuple):
                activation = output[0]
            else:
                activation = output
        else:
            # Custom hook point - assume output for now
            activation = output

        # Store activation (detach to avoid gradient computation)
        if isinstance(activation, torch.Tensor):
            self.activations.append(activation.detach().cpu())

    def register(self, model: PreTrainedModel):
        """Register hook with model."""
        # Find the target layer
        target_module = model
        for part in self.layer_name.split("."):
            target_module = getattr(target_module, part)

        # Register the hook
        self.hook_handle = target_module.register_forward_hook(self.hook_fn)
        logger.debug(f"Registered hook on {self.layer_name}")

    def remove(self):
        """Remove the hook."""
        if self.hook_handle is not None:
            self.hook_handle.remove()
            self.hook_handle = None

    def get_activations(self) -> List[torch.Tensor]:
        """Get collected activations."""
        return self.activations

    def clear(self):
        """Clear collected activations."""
        self.activations.clear()


class ActivationsStore:
    """
    Store for managing model activations during SAE training.

    Handles activation collection, batching, and streaming for efficient
    SAE training on large datasets.
    """

    def __init__(
        self,
        model: PreTrainedModel,
        tokenizer: PreTrainedTokenizer,
        config: SAEConfig,
        dataset_manager: DatasetManager,
    ):
        """
        Initialize activations store.

        Args:
            model: Transformer model to extract activations from
            tokenizer: Tokenizer for the model
            config: SAE configuration
            dataset_manager: Dataset manager for data loading
        """
        self.model = model
        self.tokenizer = tokenizer
        self.config = config
        self.dataset_manager = dataset_manager

        # Activation collection
        self.hook = None
        self.activation_buffer = Queue(maxsize=config.training.batch_size * 10)
        self.buffer_thread = None
        self.stop_buffering = threading.Event()

        # Statistics
        self.total_tokens_processed = 0
        self.activation_stats = defaultdict(list)

    def setup_hooks(self) -> None:
        """Setup activation hooks on the model."""
        # Determine hook name
        hook_name = self.config.hook_name
        if "{layer}" in hook_name:
            hook_name = hook_name.format(layer=self.config.hook_layer)

        # Create and register hook
        self.hook = ActivationHook(hook_name, "output")

        # For DataParallel models, register hook on the underlying module
        model_to_hook = (
            self.model.module if hasattr(self.model, "module") else self.model
        )
        self.hook.register(model_to_hook)

        logger.info(f"Setup activation hook on {hook_name}")

    def remove_hooks(self) -> None:
        """Remove activation hooks from the model."""
        if self.hook is not None:
            self.hook.remove()
            self.hook = None

    def collect_activations(
        self,
        num_batches: int,
        batch_size: int = 32,
    ) -> torch.Tensor:
        """
        Collect activations from the model.

        Args:
            num_batches: Number of batches to process
            batch_size: Batch size for data loading

        Returns:
            Collected activations tensor
        """
        if self.hook is None:
            self.setup_hooks()

        # Get data loader with optimized settings for multi-GPU
        num_workers = 2 if torch.cuda.device_count() > 1 else 0
        dataloader = self.dataset_manager.get_dataloader(
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
        )

        all_activations = []

        # Clear model cache before starting to avoid tensor shape mismatches
        self._clear_model_cache()

        try:
            self.model.eval()
            with torch.no_grad():
                for batch_idx, batch in enumerate(dataloader):
                    if batch_idx >= num_batches:
                        break

                    # Move batch to model device
                    input_ids = batch["input_ids"].to(self.model.device)

                    # Force consistent sequence length to avoid rotary embedding cache issues
                    # Pad or truncate to a fixed length that's compatible with the model
                    target_seq_len = 128  # Use a power of 2 that's likely cached

                    # Ensure input_ids is 2D
                    if input_ids.dim() == 1:
                        input_ids = input_ids.unsqueeze(0)  # Add batch dimension
                    elif input_ids.dim() > 2:
                        # Flatten extra dimensions
                        input_ids = input_ids.view(-1, input_ids.shape[-1])

                    batch_size, current_seq_len = input_ids.shape

                    if current_seq_len != target_seq_len:
                        if current_seq_len > target_seq_len:
                            # Truncate
                            input_ids = input_ids[:, :target_seq_len]
                        else:
                            # Pad with pad token
                            pad_token_id = getattr(
                                self.tokenizer,
                                "pad_token_id",
                                self.tokenizer.eos_token_id,
                            )
                            padding = torch.full(
                                (batch_size, target_seq_len - current_seq_len),
                                pad_token_id,
                                dtype=input_ids.dtype,
                                device=input_ids.device,
                            )
                            input_ids = torch.cat([input_ids, padding], dim=1)

                    logger.debug(f"  Normalized input_ids shape: {input_ids.shape}")

                    # Forward pass to collect activations
                    _ = self.model(input_ids)

                    # Get activations from hook
                    if self.hook.activations:
                        batch_activations = self.hook.activations[-1]
                        all_activations.append(batch_activations)

                    # Clear hook activations
                    self.hook.clear()

                    # Update statistics
                    self.total_tokens_processed += input_ids.numel()

                    if batch_idx % 10 == 0:
                        logger.debug(f"Processed batch {batch_idx}/{num_batches}")

        except Exception as e:
            logger.error(f"Error during activation collection: {e}")
            raise
        finally:
            self.remove_hooks()

        if not all_activations:
            raise RuntimeError("No activations collected")

        # Concatenate all activations
        activations = torch.cat(all_activations, dim=0)

        logger.info(f"Collected activations shape: {activations.shape}")
        return activations

    def start_streaming(self, batch_size: int = 32) -> None:
        """
        Start streaming activations in background thread.

        Args:
            batch_size: Batch size for streaming
        """
        if self.buffer_thread is not None:
            logger.warning("Streaming already started")
            return

        self.stop_buffering.clear()
        self.buffer_thread = threading.Thread(
            target=self._stream_activations, args=(batch_size,), daemon=True
        )
        self.buffer_thread.start()

        logger.info("Started activation streaming")

    def stop_streaming(self) -> None:
        """Stop streaming activations."""
        if self.buffer_thread is None:
            return

        self.stop_buffering.set()
        self.buffer_thread.join(timeout=5.0)
        self.buffer_thread = None

        # Clear remaining items in buffer
        while not self.activation_buffer.empty():
            try:
                self.activation_buffer.get_nowait()
            except Empty:
                break

        logger.info("Stopped activation streaming")

    def _clear_model_cache(self) -> None:
        """Clear model cache and ensure clean state."""
        logger.debug("Clearing model cache to avoid tensor shape mismatches")

        # Clear various types of cached states
        if hasattr(self.model, "past_key_values"):
            self.model.past_key_values = None
        if hasattr(self.model, "_past_key_values"):
            self.model._past_key_values = None
        if hasattr(self.model, "cache"):
            self.model.cache = None

        # Clear transformer-specific caches
        if hasattr(self.model, "transformer"):
            for layer in getattr(self.model.transformer, "h", []):
                if hasattr(layer, "attn") and hasattr(layer.attn, "past_key_value"):
                    layer.attn.past_key_value = None

        # Clear LLaMA-specific caches and rotary embeddings
        if hasattr(self.model, "model"):
            if hasattr(self.model.model, "layers"):
                for i, layer in enumerate(self.model.model.layers):
                    if hasattr(layer, "self_attn"):
                        # Clear attention caches
                        for attr in ["past_key_value", "_past_key_value", "cache"]:
                            if hasattr(layer.self_attn, attr):
                                setattr(layer.self_attn, attr, None)

                        # Clear rotary embedding cache that causes sequence length issues
                        if hasattr(layer.self_attn, "rotary_emb"):
                            rotary_emb = layer.self_attn.rotary_emb
                            # Clear all possible cached cos/sin tensors
                            cache_attrs = [
                                "cos_cached",
                                "sin_cached",
                                "_cos_cached",
                                "_sin_cached",
                                "cos_cache",
                                "sin_cache",
                                "_cos_cache",
                                "_sin_cache",
                                "cache",
                                "_cache",
                                "cos",
                                "sin",
                            ]
                            for attr in cache_attrs:
                                if hasattr(rotary_emb, attr):
                                    setattr(rotary_emb, attr, None)
                                    logger.debug(
                                        f"Cleared rotary_emb.{attr} for layer {i}"
                                    )

                        # Also try to clear any cached position embeddings at the attention level
                        pos_emb_attrs = ["cos", "sin", "cos_cached", "sin_cached"]
                        for attr in pos_emb_attrs:
                            if hasattr(layer.self_attn, attr):
                                setattr(layer.self_attn, attr, None)
                                logger.debug(f"Cleared self_attn.{attr} for layer {i}")

        # Try to clear any global rotary embedding caches
        def clear_rotary_caches(module, prefix=""):
            """Recursively clear rotary embedding caches."""
            for name, child in module.named_children():
                full_name = f"{prefix}.{name}" if prefix else name

                # Check if this module has rotary embedding attributes
                if "rotary" in name.lower() or "pos" in name.lower():
                    cache_attrs = [
                        "cos_cached",
                        "sin_cached",
                        "_cos_cached",
                        "_sin_cached",
                        "cos_cache",
                        "sin_cache",
                        "_cos_cache",
                        "_sin_cache",
                        "cache",
                        "_cache",
                        "cos",
                        "sin",
                    ]
                    for attr in cache_attrs:
                        if hasattr(child, attr):
                            setattr(child, attr, None)
                            logger.debug(f"Cleared {full_name}.{attr}")

                # Recurse into child modules
                clear_rotary_caches(child, full_name)

        clear_rotary_caches(self.model)

        # Clear CUDA cache if available
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        # Ensure model is in eval mode and disable gradient checkpointing temporarily
        self.model.eval()

        # Temporarily disable gradient checkpointing if it's enabled
        self._original_gradient_checkpointing = getattr(
            self.model, "gradient_checkpointing", False
        )
        if hasattr(self.model, "gradient_checkpointing"):
            self.model.gradient_checkpointing = False

        logger.debug("Model cache clearing completed")

    def _stream_activations(self, batch_size: int) -> None:
        """Background thread function for streaming activations."""
        if self.hook is None:
            self.setup_hooks()

        # Clear model cache before starting
        self._clear_model_cache()

        # Use optimized data loading for multi-GPU
        num_workers = 2 if torch.cuda.device_count() > 1 else 0
        dataloader = self.dataset_manager.get_dataloader(
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
        )

        batch_count = 0
        successful_batches = 0

        try:
            self.model.eval()
            with torch.no_grad():
                for batch in dataloader:
                    if self.stop_buffering.is_set():
                        break

                    batch_count += 1

                    try:
                        # Move batch to model device
                        input_ids = batch["input_ids"].to(self.model.device)

                        # Force consistent sequence length to avoid rotary embedding cache issues
                        target_seq_len = 128  # Use a power of 2 that's likely cached

                        # Ensure input_ids is 2D
                        if input_ids.dim() == 1:
                            input_ids = input_ids.unsqueeze(0)  # Add batch dimension
                        elif input_ids.dim() > 2:
                            # Flatten extra dimensions
                            input_ids = input_ids.view(-1, input_ids.shape[-1])

                        batch_size, current_seq_len = input_ids.shape

                        if current_seq_len != target_seq_len:
                            if current_seq_len > target_seq_len:
                                # Truncate
                                input_ids = input_ids[:, :target_seq_len]
                            else:
                                # Pad with pad token
                                pad_token_id = getattr(
                                    self.tokenizer,
                                    "pad_token_id",
                                    self.tokenizer.eos_token_id,
                                )
                                padding = torch.full(
                                    (batch_size, target_seq_len - current_seq_len),
                                    pad_token_id,
                                    dtype=input_ids.dtype,
                                    device=input_ids.device,
                                )
                                input_ids = torch.cat([input_ids, padding], dim=1)

                        # Log batch info for debugging
                        logger.debug(
                            f"Processing batch {batch_count}: normalized input_ids shape {input_ids.shape}"
                        )

                        # Clear any cached states that might cause dimension mismatches
                        if hasattr(self.model, "past_key_values"):
                            self.model.past_key_values = None

                        # Ensure model is in eval mode and clear gradients
                        self.model.eval()
                        if hasattr(self.model, "zero_grad"):
                            self.model.zero_grad()

                        # Forward pass with explicit attention_mask to avoid shape issues
                        attention_mask = torch.ones_like(input_ids)

                        # Log detailed tensor information for debugging
                        logger.debug(f"  input_ids device: {input_ids.device}")
                        logger.debug(f"  attention_mask shape: {attention_mask.shape}")
                        logger.debug(
                            f"  Model device: {next(self.model.parameters()).device}"
                        )

                        try:
                            # Try forward pass with minimal parameters first
                            _ = self.model(
                                input_ids=input_ids,
                                attention_mask=attention_mask,
                                use_cache=False,
                                output_hidden_states=False,
                                return_dict=True,
                            )
                        except RuntimeError as forward_error:
                            # If we get a tensor shape error, try with a smaller batch
                            if "size of tensor" in str(
                                forward_error
                            ) and "must match" in str(forward_error):
                                logger.warning(
                                    f"Tensor shape mismatch, trying with batch size 1: {forward_error}"
                                )
                                try:
                                    # Try with just the first sample
                                    single_input = input_ids[:1]
                                    single_mask = attention_mask[:1]
                                    outputs = self.model(
                                        input_ids=single_input,
                                        attention_mask=single_mask,
                                        use_cache=False,
                                        output_hidden_states=False,
                                        return_dict=True,
                                    )
                                    logger.info(
                                        "Successfully processed with batch size 1"
                                    )
                                except Exception as single_error:
                                    logger.error(
                                        f"Even single sample failed: {single_error}"
                                    )
                                    raise forward_error
                            else:
                                logger.error(f"Forward pass failed: {forward_error}")
                                logger.error(
                                    f"Input shapes - input_ids: {input_ids.shape}, attention_mask: {attention_mask.shape}"
                                )
                                raise forward_error
                        except Exception as forward_error:
                            logger.error(f"Forward pass failed: {forward_error}")
                            logger.error(
                                f"Input shapes - input_ids: {input_ids.shape}, attention_mask: {attention_mask.shape}"
                            )
                            raise forward_error

                        # Get activations
                        if self.hook.activations:
                            activations = self.hook.activations[-1]

                            # Validate activation shape
                            if activations.dim() < 2:
                                logger.warning(
                                    f"Unexpected activation shape: {activations.shape}. Skipping batch."
                                )
                                self.hook.clear()
                                continue

                            logger.debug(
                                f"Collected activations shape: {activations.shape}"
                            )

                            # Add to buffer (blocking if full)
                            try:
                                self.activation_buffer.put(activations, timeout=1.0)
                                successful_batches += 1
                            except Exception as buffer_e:
                                logger.debug(
                                    f"Buffer full or error adding activations: {buffer_e}"
                                )
                                # Buffer full, skip this batch
                                pass
                        else:
                            logger.debug(
                                f"No activations captured for batch {batch_count}"
                            )

                        self.hook.clear()

                    except Exception as batch_e:
                        logger.warning(
                            f"Error processing batch {batch_count}: {batch_e}"
                        )
                        # Clear hook and continue with next batch
                        self.hook.clear()
                        continue

        except Exception as e:
            logger.error(f"Error in activation streaming: {e}")
            import traceback

            logger.error(f"Full traceback: {traceback.format_exc()}")
        finally:
            logger.info(
                f"Activation streaming finished. Processed {batch_count} batches, {successful_batches} successful."
            )
            self.remove_hooks()

    def get_next_batch(self, timeout: float = 1.0) -> Optional[torch.Tensor]:
        """
        Get next batch of activations from stream.

        Args:
            timeout: Timeout for getting batch

        Returns:
            Batch of activations or None if timeout
        """
        try:
            return self.activation_buffer.get(timeout=timeout)
        except Empty:
            return None

    def get_activation_stats(self) -> Dict[str, Any]:
        """
        Get statistics about collected activations.

        Returns:
            Dictionary of activation statistics
        """
        return {
            "total_tokens_processed": self.total_tokens_processed,
            "buffer_size": self.activation_buffer.qsize(),
            "is_streaming": self.buffer_thread is not None,
            "is_streaming_alive": self.buffer_thread is not None
            and self.buffer_thread.is_alive(),
        }

    def is_streaming_healthy(self) -> bool:
        """
        Check if activation streaming is healthy.

        Returns:
            True if streaming is active and thread is alive
        """
        return (
            self.buffer_thread is not None
            and self.buffer_thread.is_alive()
            and not self.stop_buffering.is_set()
        )

    def restart_streaming_if_needed(self, batch_size: int = 32) -> bool:
        """
        Restart streaming if the thread has died.

        Args:
            batch_size: Batch size for streaming

        Returns:
            True if streaming was restarted, False if already healthy
        """
        if not self.is_streaming_healthy():
            logger.warning(
                "Activation streaming thread appears to have died. Restarting..."
            )
            self.stop_streaming()
            self.start_streaming(batch_size)
            return True
        return False

    def compute_activation_statistics(
        self, activations: torch.Tensor, compute_percentiles: bool = True
    ) -> Dict[str, torch.Tensor]:
        """
        Compute statistics for a batch of activations.

        Args:
            activations: Activation tensor
            compute_percentiles: Whether to compute percentile statistics

        Returns:
            Dictionary of statistics
        """
        stats = {}

        # Basic statistics
        stats["mean"] = activations.mean(dim=0)
        stats["std"] = activations.std(dim=0)
        stats["min"] = activations.min(dim=0)[0]
        stats["max"] = activations.max(dim=0)[0]

        # Sparsity statistics
        stats["sparsity"] = (activations == 0).float().mean(dim=0)
        stats["active_fraction"] = (activations > 0).float().mean(dim=0)

        if compute_percentiles:
            # Percentile statistics
            percentiles = [10, 25, 50, 75, 90, 95, 99]
            for p in percentiles:
                stats[f"p{p}"] = torch.quantile(activations, p / 100, dim=0)

        return stats

    def __enter__(self):
        """Context manager entry."""
        self.setup_hooks()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.remove_hooks()
        self.stop_streaming()
